/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo .logo {
    height: 35px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #666;
}

/* Page Title */
.page-title {
    background: #f8f9fa;
    padding: 40px 0;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
}

.page-title h1 {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* Temple Sections */
.temple-section {
    padding: 60px 0;
}

.temple-section h2 {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin-bottom: 40px;
    text-align: center;
}

/* Temple Gallery */
.temple-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.temple-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    aspect-ratio: 4/3;
}

.temple-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.temple-item img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    background: white;
    display: block;
}

/* Enquire Section */
.enquire-section {
    background: #f8f9fa;
    padding: 60px 0;
    text-align: center;
}

.enquire-section h2 {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.enquire-section p {
    font-size: 16px;
    color: #666;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.enquire-form {
    max-width: 600px;
    margin: 0 auto;
    text-align: left;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row:last-of-type {
    grid-template-columns: 1fr;
}

.enquire-form input,
.enquire-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.enquire-form input:focus,
.enquire-form textarea:focus {
    outline: none;
    border-color: #333;
}

.enquire-btn {
    background: #333;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
    margin-top: 20px;
}

.enquire-btn:hover {
    background: #555;
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 30px;
}

.footer-brand .footer-logo {
    height: 35px;
    margin-bottom: 20px;
    filter: brightness(0) invert(1);
}

.footer-contact p {
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.5;
}

.footer-contact a {
    color: white;
    text-decoration: none;
}

.footer-contact a:hover {
    text-decoration: underline;
}

.footer-social {
    margin-top: 20px;
}

.footer-social a {
    color: #ccc;
    text-decoration: none;
    margin-right: 15px;
    font-size: 14px;
}

.footer-social a:hover {
    color: white;
}

.footer-links h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: white;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links ul li {
    margin-bottom: 8px;
}

.footer-links ul li a {
    color: #ccc;
    text-decoration: none;
    font-size: 14px;
}

.footer-links ul li a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 20px;
    text-align: center;
}

.footer-bottom p {
    margin: 5px 0;
    font-size: 13px;
    color: #ccc;
}

.footer-bottom a {
    color: white;
    text-decoration: none;
}

.footer-bottom a:hover {
    text-decoration: underline;
}
/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 65px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 20px 0;
        gap: 15px;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .temple-gallery {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .page-title h1 {
        font-size: 24px;
    }

    .temple-section h2 {
        font-size: 22px;
    }

    .temple-section {
        padding: 40px 0;
    }

    .enquire-section {
        padding: 40px 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .temple-gallery {
        gap: 15px;
    }

    .temple-item {
        border-radius: 6px;
    }

    .page-title {
        padding: 30px 0;
    }

    .page-title h1 {
        font-size: 20px;
    }

    .temple-section h2 {
        font-size: 20px;
        margin-bottom: 30px;
    }

    .temple-section {
        padding: 30px 0;
    }

    .enquire-section {
        padding: 30px 0;
    }

    .enquire-section h2 {
        font-size: 20px;
    }
}








