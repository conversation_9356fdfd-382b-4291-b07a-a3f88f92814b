/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo .logo {
    height: 35px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #666;
}

/* Hero Section */
.hero {
    position: relative;
    height: 70vh;
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.95) 0%, rgba(233, 236, 239, 0.9) 100%),
                url('https://pattharkari.com/wp-content/uploads/2025/01/Untitled-design-1.jpg') center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #2c3e50;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.85);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.hero-text h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    line-height: 1.7;
    color: #6c757d;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-badges {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.badge {
    background: white;
    border: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.badge-icon {
    font-size: 1.2rem;
}

.hero-cta-group {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary {
    background: #2c3e50;
    color: white;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #495057;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #2c3e50;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    border: 1px solid #2c3e50;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #2c3e50;
    color: white;
    transform: translateY(-2px);
}

/* Legacy Section */
.legacy-section {
    padding: 6rem 0;
    background: #f8f9fa;
}

.legacy-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.legacy-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.lead {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.legacy-text p {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.legacy-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.legacy-image {
    position: relative;
}

.legacy-image img {
    width: 100%;
    height: auto;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.image-caption {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
}

/* Temple Category Sections */
.temple-category {
    padding: 6rem 0;
}

.bg-light {
    background: #f8f9fa;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-tag {
    display: inline-block;
    background: #e9ecef;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.section-description {
    font-size: 1rem;
    color: #6c757d;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
}

/* Temple Showcase */
.temple-showcase {
    margin-top: 3rem;
}

.temple-showcase .featured {
    margin-bottom: 3rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.temple-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.temple-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid #e9ecef;
}

.temple-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.temple-image {
    position: relative;
    width: 100%;
    aspect-ratio: 4/3;
    overflow: hidden;
    background: #f8f9fa;
}

.featured .temple-image {
    aspect-ratio: 16/10;
}

.temple-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    background: white;
}

.temple-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #2c3e50;
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 3;
}

.temple-info {
    padding: 2.5rem;
}

.featured .temple-info {
    padding: 3rem;
}

.temple-info h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.temple-info p {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.temple-specs {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.spec {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.spec:last-child {
    margin-bottom: 0;
}

.spec-label {
    font-weight: 600;
    color: #2c3e50;
}

.spec-value {
    color: #666;
}

.temple-features {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.feature {
    background: #f8f9fa;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid #e9ecef;
}

/* Info Cards */
.manasthamb-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.info-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
}

.info-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.info-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.info-card p {
    color: #666;
    line-height: 1.6;
}

/* Feature Sections */
.slide-temple-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.feature-highlight {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.feature-highlight:hover {
    transform: translateY(-5px);
}

.highlight-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.feature-highlight h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.feature-highlight p {
    color: #666;
    line-height: 1.6;
}

.vedi-significance {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.significance-card {
    background: #2c3e50;
    color: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
}

.significance-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.significance-card p {
    line-height: 1.6;
    opacity: 0.95;
}

/* Enquiry Section */
.enquiry-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.enquiry-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.enquiry-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.enquiry-info .lead {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 3rem;
    line-height: 1.7;
}

.process-steps {
    margin-bottom: 3rem;
}

.step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.step-number {
    width: 40px;
    height: 40px;
    background: #2c3e50;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 1rem;
    flex-shrink: 0;
}

.step-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.step-content p {
    color: #666;
    line-height: 1.6;
    font-size: 0.95rem;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
}

.contact-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.contact-text strong {
    display: block;
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.contact-text span {
    color: #666;
    font-size: 0.9rem;
}

.enquiry-form {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.form-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.form-header h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: #666;
    font-size: 0.95rem;
}

.quote-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2c3e50;
    box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.1);
}

.submit-btn {
    background: #2c3e50;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.submit-btn:hover {
    background: #495057;
    transform: translateY(-2px);
}

.btn-icon {
    transition: transform 0.3s ease;
}

.submit-btn:hover .btn-icon {
    transform: translateX(3px);
}

.form-note {
    text-align: center;
    margin-top: 1rem;
    color: #666;
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 30px;
}

.footer-brand .footer-logo {
    height: 35px;
    margin-bottom: 20px;
    filter: brightness(0) invert(1);
}

.footer-contact p {
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.5;
}

.footer-contact a {
    color: white;
    text-decoration: none;
}

.footer-contact a:hover {
    text-decoration: underline;
}

.footer-social {
    margin-top: 20px;
}

.footer-social a {
    color: #ccc;
    text-decoration: none;
    margin-right: 15px;
    font-size: 14px;
}

.footer-social a:hover {
    color: white;
}

.footer-links h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: white;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links ul li {
    margin-bottom: 8px;
}

.footer-links ul li a {
    color: #ccc;
    text-decoration: none;
    font-size: 14px;
}

.footer-links ul li a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 20px;
    text-align: center;
}

.footer-bottom p {
    margin: 5px 0;
    font-size: 13px;
    color: #ccc;
}

.footer-bottom a {
    color: white;
    text-decoration: none;
}

.footer-bottom a:hover {
    text-decoration: underline;
}
/* Responsive Design */
@media (max-width: 1024px) {
    .legacy-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .legacy-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .temple-showcase .featured {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .enquiry-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 65px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 20px 0;
        gap: 15px;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .hero-badges {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-cta-group {
        flex-direction: column;
        gap: 1rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .legacy-stats {
        grid-template-columns: 1fr;
    }

    .temple-grid {
        grid-template-columns: 1fr;
    }

    .manasthamb-info,
    .slide-temple-features,
    .vedi-significance {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .enquiry-info h2 {
        font-size: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .temple-category {
        padding: 4rem 0;
    }

    .enquiry-section {
        padding: 4rem 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .badge {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .temple-category {
        padding: 3rem 0;
    }

    .section-header h2 {
        font-size: 1.75rem;
    }

    .temple-grid {
        gap: 1.5rem;
    }

    .temple-info {
        padding: 2rem;
    }

    .featured .temple-info {
        padding: 2rem;
    }

    .enquiry-form {
        padding: 2rem;
    }

    .form-header h3 {
        font-size: 1.5rem;
    }

    .step {
        padding: 1rem;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .contact-item {
        padding: 0.75rem;
    }

    .enquiry-section {
        padding: 3rem 0;
    }
}








