/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
    background: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Top Bar */
.top-bar {
    background: #2c3e50;
    color: white;
    padding: 0.5rem 0;
    font-size: 0.875rem;
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info {
    display: flex;
    gap: 2rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #f39c12;
}

/* Navigation */
.navbar {
    position: sticky;
    top: 0;
    width: 100%;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo .logo {
    height: 45px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-menu li {
    position: relative;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 0.5rem 0;
    transition: color 0.3s ease;
}

.nav-menu a:hover,
.nav-menu .active a {
    color: #f39c12;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-radius: 8px;
    padding: 1rem 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    padding: 0;
}

.dropdown-menu a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: #666;
    font-weight: 400;
}

.dropdown-menu a:hover {
    background: #f8f9fa;
    color: #f39c12;
}

.cta-btn {
    background: #f39c12;
    color: white !important;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 600;
}

.cta-btn:hover {
    background: #e67e22;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.9) 0%, rgba(52, 73, 94, 0.8) 100%),
                url('https://pattharkari.com/wp-content/uploads/2025/01/Untitled-design-1.jpg') center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    overflow: hidden;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 2rem;
}

.hero-text h1 {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    letter-spacing: -1px;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    line-height: 1.7;
    font-weight: 400;
}

.hero-badges {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.badge {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 1rem 1.5rem;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.badge-icon {
    font-size: 1.2rem;
}

.hero-cta-group {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary {
    background: #f39c12;
    color: white;
    padding: 1rem 2.5rem;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(243, 156, 18, 0.3);
}

.btn-primary:hover {
    background: #e67e22;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    padding: 1rem 2.5rem;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    border: 2px solid white;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: white;
    color: #2c3e50;
    transform: translateY(-3px);
}

.hero-scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: white;
    opacity: 0.8;
    z-index: 2;
}

.scroll-arrow {
    margin-top: 0.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}
/* Legacy Section */
.legacy-section {
    padding: 6rem 0;
    background: #f8f9fa;
}

.legacy-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.legacy-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    line-height: 1.3;
}

.lead {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.legacy-text p {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.legacy-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    color: #f39c12;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.legacy-image {
    position: relative;
}

.legacy-image img {
    width: 100%;
    height: auto;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.image-caption {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Temple Category Sections */
.temple-category {
    padding: 6rem 0;
}

.bg-light {
    background: #f8f9fa;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-tag {
    display: inline-block;
    background: #f39c12;
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.section-header h2 {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.section-description {
    font-size: 1.1rem;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
}

/* Temple Showcase */
.temple-showcase {
    margin-top: 3rem;
}

.temple-showcase .featured {
    margin-bottom: 3rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.temple-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.temple-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.4s ease;
    position: relative;
}

.temple-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.temple-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.featured .temple-image {
    height: 400px;
}

.temple-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.temple-card:hover .temple-image img {
    transform: scale(1.08);
}

.temple-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #f39c12;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 3;
}

.temple-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.9) 0%, rgba(52, 73, 94, 0.8) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
}

.temple-card:hover .temple-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: white;
    padding: 2rem;
}

.overlay-content h4 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.overlay-content p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.btn-view-details {
    background: #f39c12;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-view-details:hover {
    background: #e67e22;
    transform: translateY(-2px);
}

.temple-info {
    padding: 2.5rem;
}

.featured .temple-info {
    padding: 3rem;
}

.temple-info h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.temple-info p {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.7;
    font-size: 1rem;
}

.temple-specs {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.spec {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.spec:last-child {
    margin-bottom: 0;
}

.spec-label {
    font-weight: 600;
    color: #2c3e50;
}

.spec-value {
    color: #666;
}

.temple-features {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.feature {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    padding: 0.6rem 1.2rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 500;
    box-shadow: 0 3px 10px rgba(243, 156, 18, 0.3);
}

/* Manasthamb Info Cards */
.manasthamb-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.info-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
}

.info-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.info-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.info-card p {
    color: #666;
    line-height: 1.6;
}

/* Additional Feature Sections */
.slide-temple-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.feature-highlight {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.feature-highlight:hover {
    transform: translateY(-5px);
}

.highlight-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.feature-highlight h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.feature-highlight p {
    color: #666;
    line-height: 1.6;
}

.vedi-significance {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.significance-card {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
}

.significance-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.significance-card p {
    line-height: 1.6;
    opacity: 0.95;
}

/* Why Choose Section */
.why-choose-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
}

.why-choose-section .section-header h2 {
    color: white;
}

.why-choose-section .section-description {
    color: #bdc3c7;
}

.why-choose-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.why-choose-section .feature-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2.5rem;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.why-choose-section .feature-card:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.why-choose-section .feature-icon {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: center;
}

.icon-bg {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    box-shadow: 0 10px 25px rgba(243, 156, 18, 0.3);
}

.why-choose-section .feature-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
}

.why-choose-section .feature-card p {
    color: #bdc3c7;
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    color: #ecf0f1;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

/* Enquiry Section */
.enquiry-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.enquiry-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.enquiry-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    line-height: 1.3;
}

.enquiry-info .lead {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 3rem;
    line-height: 1.7;
}

.process-steps {
    margin-bottom: 3rem;
}

.step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 1rem;
    flex-shrink: 0;
}

.step-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.step-content p {
    color: #666;
    line-height: 1.6;
    font-size: 0.95rem;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
}

.contact-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.contact-text strong {
    display: block;
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.contact-text span {
    color: #666;
    font-size: 0.9rem;
}

.enquiry-form {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.form-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.form-header h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: #666;
    font-size: 0.95rem;
}

.quote-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #f39c12;
    box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.1);
}

.submit-btn {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    border: none;
    padding: 1.25rem 2rem;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-icon {
    transition: transform 0.3s ease;
}

.submit-btn:hover .btn-icon {
    transform: translateX(3px);
}

.form-note {
    text-align: center;
    margin-top: 1rem;
    color: #666;
}
/* Footer */
.footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 4rem 0 0;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-brand {
    padding-right: 2rem;
}

.footer-logo {
    height: 50px;
    margin-bottom: 1.5rem;
    filter: brightness(0) invert(1);
}

.footer-tagline {
    font-size: 1.1rem;
    font-weight: 600;
    color: #f39c12;
    margin-bottom: 1rem;
}

.footer-description {
    color: #bdc3c7;
    line-height: 1.7;
    margin-bottom: 2rem;
    font-size: 0.95rem;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #bdc3c7;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.social-link:hover {
    background: rgba(243, 156, 18, 0.2);
    color: #f39c12;
    transform: translateY(-2px);
}

.footer-links h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #f39c12;
}

.footer-links ul {
    list-style: none;
}

.footer-links ul li {
    margin-bottom: 0.75rem;
}

.footer-links ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.footer-links ul li a:hover {
    color: #f39c12;
    padding-left: 5px;
}

.footer-contact .contact-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-detail {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-detail .contact-icon {
    font-size: 1.2rem;
    margin-top: 0.2rem;
}

.contact-detail strong {
    display: block;
    color: #f39c12;
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.contact-detail p {
    color: #bdc3c7;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.5;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    background: rgba(0, 0, 0, 0.2);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-bottom p {
    color: #bdc3c7;
    font-size: 0.9rem;
    margin: 0;
}

.footer-credits {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #95a5a6;
    font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .top-bar-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .legacy-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .legacy-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .temple-showcase .featured {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .enquiry-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .footer-brand {
        grid-column: 1 / -1;
        padding-right: 0;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .top-bar {
        display: none;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
        z-index: 999;
        max-height: calc(100vh - 80px);
        overflow-y: auto;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background: #f8f9fa;
        margin-top: 0.5rem;
        border-radius: 8px;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .hero-badges {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-cta-group {
        flex-direction: column;
        gap: 1rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .legacy-stats {
        grid-template-columns: 1fr;
    }

    .temple-grid {
        grid-template-columns: 1fr;
    }

    .manasthamb-info,
    .slide-temple-features,
    .vedi-significance {
        grid-template-columns: 1fr;
    }

    .why-choose-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .enquiry-info h2 {
        font-size: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .footer-credits {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .badge {
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .temple-category {
        padding: 4rem 0;
    }

    .section-header h2 {
        font-size: 1.75rem;
    }

    .section-description {
        font-size: 1rem;
    }

    .temple-grid {
        gap: 1.5rem;
    }

    .temple-card {
        border-radius: 16px;
    }

    .temple-info {
        padding: 2rem;
    }

    .featured .temple-info {
        padding: 2rem;
    }

    .enquiry-form {
        padding: 2rem;
    }

    .form-header h3 {
        font-size: 1.5rem;
    }

    .why-choose-section {
        padding: 4rem 0;
    }

    .why-choose-section .feature-card {
        padding: 2rem;
    }

    .step {
        padding: 1rem;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .contact-item {
        padding: 0.75rem;
    }

    .footer {
        padding: 3rem 0 0;
    }

    .footer-content {
        margin-bottom: 2rem;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.temple-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scroll Animations */
.temple-card:nth-child(1) { animation-delay: 0.1s; }
.temple-card:nth-child(2) { animation-delay: 0.2s; }
.temple-card:nth-child(3) { animation-delay: 0.3s; }

/* Focus States for Accessibility */
.view-details:focus,
.hero-cta:focus,
.cta-btn:focus,
.submit-btn:focus {
    outline: 2px solid #d4af37;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .navbar,
    .hamburger,
    .temple-overlay,
    .contact-form,
    .footer {
        display: none;
    }

    .hero {
        page-break-after: always;
    }

    .temple-card {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
}