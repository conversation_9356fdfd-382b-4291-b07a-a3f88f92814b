/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo .logo {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #d4af37;
}

.cta-btn {
    background: #d4af37;
    color: white !important;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.cta-btn:hover {
    background: #b8941f;
    transform: translateY(-2px);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding-top: 80px;
}

.hero-content {
    flex: 1;
    padding-right: 2rem;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.25rem;
    color: #666;
    margin-bottom: 2rem;
}

.hero-stats {
    display: flex;
    gap: 3rem;
    margin-bottom: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #d4af37;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-cta {
    display: inline-block;
    background: #d4af37;
    color: white;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.hero-cta:hover {
    background: #b8941f;
    transform: translateY(-2px);
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}
/* Temple Category Sections */
.temple-category {
    padding: 5rem 0;
}

.bg-light {
    background: #f8f9fa;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.temple-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.temple-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.temple-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.temple-image {
    position: relative;
    height: 280px;
    overflow: hidden;
}

.temple-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.temple-card:hover .temple-image img {
    transform: scale(1.05);
}

.temple-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.temple-card:hover .temple-overlay {
    opacity: 1;
}

.view-details {
    background: #d4af37;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-details:hover {
    background: #b8941f;
    transform: translateY(-2px);
}

.temple-info {
    padding: 2rem;
}

.temple-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
}

.temple-info p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.temple-features {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.feature {
    background: #f8f9fa;
    color: #666;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Features Section */
.features-section {
    padding: 5rem 0;
    background: #2c3e50;
    color: white;
}

.features-section .section-header h2 {
    color: white;
}

.features-section .section-header p {
    color: #bdc3c7;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
}

.feature-icon {
    color: #d4af37;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: center;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.feature-card p {
    color: #bdc3c7;
    line-height: 1.6;
}

/* Contact Section */
.contact-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.contact-info p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-details {
    space-y: 1rem;
}

.contact-item {
    margin-bottom: 1rem;
    color: #666;
}

.contact-item strong {
    color: #2c3e50;
}

.contact-form {
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
}

.contact-form h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #d4af37;
}

.submit-btn {
    width: 100%;
    background: #d4af37;
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    background: #b8941f;
    transform: translateY(-2px);
}
/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-logo img {
    height: 40px;
    margin-bottom: 1rem;
    filter: brightness(0) invert(1);
}

.footer-logo p {
    color: #bdc3c7;
    line-height: 1.6;
}

.footer-links h4,
.footer-contact h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #d4af37;
}

.footer-links ul {
    list-style: none;
}

.footer-links ul li {
    margin-bottom: 0.5rem;
}

.footer-links ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links ul li a:hover {
    color: #d4af37;
}

.footer-contact p {
    color: #bdc3c7;
    margin-bottom: 0.5rem;
}

.footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 1rem;
    text-align: center;
    color: #bdc3c7;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 100px 2rem 2rem;
    }

    .hero-content {
        padding-right: 0;
        margin-bottom: 3rem;
    }

    .hero-stats {
        justify-content: center;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .temple-grid {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .contact-info h2 {
        font-size: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .temple-category {
        padding: 3rem 0;
    }

    .section-header h2 {
        font-size: 1.75rem;
    }

    .temple-grid {
        gap: 1.5rem;
    }

    .temple-card {
        border-radius: 12px;
    }

    .temple-info {
        padding: 1.5rem;
    }

    .contact-form {
        padding: 2rem;
    }

    .features-section {
        padding: 3rem 0;
    }

    .feature-card {
        padding: 1.5rem;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.temple-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scroll Animations */
.temple-card:nth-child(1) { animation-delay: 0.1s; }
.temple-card:nth-child(2) { animation-delay: 0.2s; }
.temple-card:nth-child(3) { animation-delay: 0.3s; }

/* Focus States for Accessibility */
.view-details:focus,
.hero-cta:focus,
.cta-btn:focus,
.submit-btn:focus {
    outline: 2px solid #d4af37;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .navbar,
    .hamburger,
    .temple-overlay,
    .contact-form,
    .footer {
        display: none;
    }

    .hero {
        page-break-after: always;
    }

    .temple-card {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
}